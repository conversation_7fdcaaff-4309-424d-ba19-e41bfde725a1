'use client';
import * as React from 'react';
import { Car, GalleryVerticalEnd, LayoutDashboard, MapPin } from 'lucide-react';

import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { TeamSwitcher } from '@/components/team-switcher';
import {
   Sidebar,
   SidebarContent,
   SidebarFooter,
   SidebarHeader,
   SidebarRail,
} from '@/components/ui/sidebar';

// Driver management data
const data = {
   user: {
      name: 'john doe',
      email: '<EMAIL>',
      avatar: '/avatars/shadcn.jpg',
   },
   teams: [
      {
         name: 'Driver Management',
         logo: GalleryVerticalEnd,
         plan: 'Enterprise',
      },
   ],
   navMain: [
      {
         title: 'Dashboard',
         url: '/dashboard',
         icon: LayoutDashboard,
         isActive: false,
      },
      {
         title: 'Drivers',
         url: '/drivers',
         icon: Car,
         isActive: true,
      },
      {
         title: 'Cities',
         url: '/cities',
         icon: MapPin,
         isActive: false,
      },
   ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
   return (
      <Sidebar collapsible='icon' {...props}>
         <SidebarHeader>
            <TeamSwitcher teams={data.teams} />
         </SidebarHeader>
         <SidebarContent>
            <NavMain items={data.navMain} />
         </SidebarContent>
         <SidebarFooter>
            <NavUser user={data.user} />
         </SidebarFooter>
         <SidebarRail />
      </Sidebar>
   );
}
