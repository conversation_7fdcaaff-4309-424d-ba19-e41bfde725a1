'use client';

import { Card } from '@/components/ui/card';
import { AppSidebar } from '@/components/app-sidebar';
import {
   Breadcrumb,
   BreadcrumbItem,
   BreadcrumbLink,
   BreadcrumbList,
   BreadcrumbPage,
   BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import { SidebarInset, SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';

import { CreateDriver } from '../components/create-driver';
import { useListDriver } from '../api/queries';
import { useState } from 'react';
import { DriverTable } from '../components/driver-table';
import { DriverFilters } from '../components/driver-filters';

export function DriverPage() {
   const [page, setPage] = useState(1);
   const [perPage] = useState(10);
   const [search, setSearch] = useState('');
   const [status, setStatus] = useState<string | undefined>(undefined);
   const [location, setLocation] = useState<string | undefined>(undefined);

   // Reset to first page when filters change
   const handleSearchChange = (value: string) => {
      setSearch(value);
      setPage(1);
   };

   const handleStatusChange = (value: string | undefined) => {
      setStatus(value);
      setPage(1);
   };

   const handleLocationChange = (value: string | undefined) => {
      setLocation(value);
      setPage(1);
   };

   // Function to clear all filters
   const handleClearFilters = () => {
      setSearch('');
      setStatus(undefined);
      setLocation(undefined);
      setPage(1);
   };

   const listDriver = useListDriver({
      page,
      perPage,
      search: search || undefined,
      status: status as 'pending' | 'active' | 'inactive' | undefined,
      location: location || undefined,
   });

   // Function to refresh the driver list data
   const handleRefreshData = () => {
      listDriver.refetch();
   };

   // Calculate driver counts by status
   const driverCounts = {
      pending: listDriver.data?.data?.filter(driver => driver.status === 'pending').length || 0,
      active: listDriver.data?.data?.filter(driver => driver.status === 'active').length || 0,
      inactive: listDriver.data?.data?.filter(driver => driver.status === 'inactive').length || 0,
   };

   return (
      <SidebarProvider>
         <AppSidebar />
         <SidebarInset>
            <header className='flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12'>
               <div className='flex items-center gap-2 px-4'>
                  <SidebarTrigger className='-ml-1' />
                  <Separator
                     orientation='vertical'
                     className='mr-2 data-[orientation=vertical]:h-4'
                  />
                  <Breadcrumb>
                     <BreadcrumbList>
                        <BreadcrumbItem className='hidden md:block'>
                           <BreadcrumbLink href='#'>Driver Management</BreadcrumbLink>
                        </BreadcrumbItem>
                        <BreadcrumbSeparator className='hidden md:block' />
                        <BreadcrumbItem>
                           <BreadcrumbPage>Drivers</BreadcrumbPage>
                        </BreadcrumbItem>
                     </BreadcrumbList>
                  </Breadcrumb>
               </div>
            </header>

            <div className='flex flex-1 flex-col gap-4 p-4 pt-0'>
               <div className='flex justify-between items-center'>
                  <h2 className='text-2xl font-semibold text-gray-900'>Drivers</h2>
                  <div className='flex items-center gap-4'>
                     {/* Driver Info Cards */}
                     <div className='flex gap-3'>
                        <div className='bg-white border border-gray-200 rounded-lg px-4 py-2 shadow-sm'>
                           <div className='text-sm text-gray-500'>Pending</div>
                           <div className='text-lg font-semibold text-yellow-600'>
                              {driverCounts.pending}
                           </div>
                        </div>
                        <div className='bg-white border border-gray-200 rounded-lg px-4 py-2 shadow-sm'>
                           <div className='text-sm text-gray-500'>Active</div>
                           <div className='text-lg font-semibold text-green-600'>
                              {driverCounts.active}
                           </div>
                        </div>
                        <div className='bg-white border border-gray-200 rounded-lg px-4 py-2 shadow-sm'>
                           <div className='text-sm text-gray-500'>Inactive</div>
                           <div className='text-lg font-semibold text-red-600'>
                              {driverCounts.inactive}
                           </div>
                        </div>
                     </div>
                     <CreateDriver />
                  </div>
               </div>

               <Card className='overflow-hidden py-4 px-4 rounded-sm'>
                  <DriverFilters
                     search={search}
                     status={status}
                     location={location}
                     onSearchChange={handleSearchChange}
                     onStatusChange={handleStatusChange}
                     onLocationChange={handleLocationChange}
                     isLoading={listDriver.isFetching && !listDriver.isLoading}
                  />

                  <DriverTable
                     data={listDriver.data}
                     isLoading={listDriver.isLoading}
                     currentPage={page}
                     onPageChange={(newPage: number) => setPage(newPage)}
                     hasFilters={!!search || !!status || !!location}
                     hasSearch={!!search}
                     hasStatus={!!status}
                     hasLocation={!!location}
                     onClearFilters={handleClearFilters}
                     onRefresh={handleRefreshData}
                  />
               </Card>
            </div>
         </SidebarInset>
      </SidebarProvider>
   );
}
